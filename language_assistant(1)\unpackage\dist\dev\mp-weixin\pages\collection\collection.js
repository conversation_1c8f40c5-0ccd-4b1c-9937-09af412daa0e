"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_CustomNavbar = common_vendor.resolveComponent("CustomNavbar");
  _component_CustomNavbar();
}
if (!Math) {
  CustumTabbar();
}
const CustumTabbar = () => "../../components/custum-tabbar/index.js";
const _sfc_main = {
  __name: "collection",
  setup(__props) {
    const tabs = common_vendor.ref([
      {
        name: "收藏对话",
        type: "dialogue"
      },
      {
        name: "收藏课件",
        type: "courseware"
      }
    ]);
    const activeTab = common_vendor.ref(0);
    const dialogueData = common_vendor.ref([
      {
        id: 1,
        title: "在火车站怎么买票对话学习",
        subtitle: "音频文字对话",
        date: "2025年1月20日 19：23",
        rating: 4.5,
        reviewCount: 24,
        cover_image: "/static/mine/avatar.jpg"
      },
      {
        id: 2,
        title: "在火车站怎么买票对话学习",
        subtitle: "音频文字对话",
        date: "2025年1月20日 19：23",
        rating: 4.5,
        reviewCount: 24,
        cover_image: "/static/mine/avatar.jpg"
      },
      {
        id: 3,
        title: "在火车站怎么买票对话学习",
        subtitle: "音频文字对话",
        date: "2025年1月20日 19：23",
        rating: 4.5,
        reviewCount: 24,
        cover_image: "/static/mine/avatar.jpg"
      }
    ]);
    const coursewareData = common_vendor.ref([{
      id: 4,
      title: "在火车站怎么买票对话学习",
      subtitle: "音频文字对话",
      date: "2025年1月20日 19：23",
      rating: 4.5,
      reviewCount: 24,
      cover_image: "/static/mine/avatar.jpg"
    }]);
    const isLoading = common_vendor.ref(false);
    const hasMore = common_vendor.ref(false);
    const currentTabData = common_vendor.ref([]);
    const switchTab = (index) => {
      activeTab.value = index;
      updateCurrentTabData();
    };
    const updateCurrentTabData = () => {
      const currentTab = tabs.value[activeTab.value];
      if (currentTab.type === "dialogue" || currentTab.type === "dialogue2") {
        currentTabData.value = dialogueData.value;
      } else {
        currentTabData.value = coursewareData.value;
      }
    };
    const getNoDataMessage = () => {
      const currentTab = tabs.value[activeTab.value];
      if (currentTab.type === "dialogue" || currentTab.type === "dialogue2") {
        return "暂无收藏对话";
      } else {
        return "暂无收藏课件";
      }
    };
    common_vendor.onReachBottom(() => {
      console.log("触底加载更多");
      if (hasMore.value && !isLoading.value)
        ;
    });
    const goToDetailPage = (item) => {
      console.log("点击：", item);
      const currentTab = tabs.value[activeTab.value];
      if (currentTab.type === "dialogue" || currentTab.type === "dialogue2") {
        common_vendor.index.navigateTo({
          url: `/pages/dialogue/detail?id=${item.id}`
        });
      } else {
        common_vendor.index.navigateTo({
          url: `/pages/courseware/detail?id=${item.id}`
        });
      }
    };
    common_vendor.onMounted(() => {
      updateCurrentTabData();
    });
    common_vendor.onShow(() => {
      updateCurrentTabData();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "我的收藏",
          changeLang: true
        }),
        b: common_vendor.f(tabs.value, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: index,
            c: common_vendor.n({
              active: activeTab.value === index
            }),
            d: common_vendor.o(($event) => switchTab(index), index)
          };
        }),
        c: currentTabData.value.length > 0
      }, currentTabData.value.length > 0 ? {
        d: common_vendor.f(currentTabData.value, (item, k0, i0) => {
          return {
            a: item.cover_image,
            b: common_vendor.t(item.title),
            c: common_vendor.t(item.subtitle),
            d: common_vendor.t(item.date),
            e: common_vendor.t(item.rating),
            f: common_vendor.f(5, (star, k1, i1) => {
              return {
                a: star,
                b: common_vendor.n({
                  filled: star <= Math.floor(item.rating)
                })
              };
            }),
            g: common_vendor.t(item.reviewCount),
            h: item.id,
            i: common_vendor.o(($event) => goToDetailPage(item), item.id)
          };
        })
      } : {
        e: common_vendor.t(getNoDataMessage())
      }, {
        f: currentTabData.value.length > 0
      }, currentTabData.value.length > 0 ? common_vendor.e({
        g: isLoading.value
      }, isLoading.value ? {} : !hasMore.value ? {} : {}, {
        h: !hasMore.value
      }) : {}, {
        i: common_vendor.p({
          current: "collection"
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-cd17183b"]]);
wx.createPage(MiniProgramPage);
